from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame
import numpy as np
from freqtrade.persistence import Trade
from datetime import datetime
import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta
from typing import Optional
import pandas as pd

class AggressiveScalpingPivot7(IStrategy):
    # Keep your original config-compatible settings
    stoploss = -0.5  # Your original stoploss - will be overridden by custom_stoploss
    can_short = True
    trailing_stop = True
    trailing_stop_positive = 0.1
    trailing_stop_positive_offset = 0.5
    
    # Scalping optimizations
    process_only_new_candles = False  # Process every tick for scalping
    startup_candle_count = 30
    
    # Scalping parameters - ultra-aggressive profit taking
    min_profit_scalp = 0.003  # 0.3% minimum profit to consider exit
    quick_profit_target = 0.005  # 0.5% quick profit target
    medium_profit_target = 0.01  # 1% medium profit target
    major_profit_target = 0.02   # 2% major profit target
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Ultra-fast RSI for scalping
        dataframe['rsi_1'] = ta.RSI(dataframe, timeperiod=1)  # Fastest RSI
        dataframe['rsi_3'] = ta.RSI(dataframe, timeperiod=2)
        dataframe['rsi_6'] = ta.RSI(dataframe, timeperiod=4)
        dataframe['rsi_14'] = ta.RSI(dataframe, timeperiod=14)
        
        # Multiple timeframe RSI for confluence
        dataframe['rsi_21'] = ta.RSI(dataframe, timeperiod=21)
        
        # Ultra-fast moving averages for scalping
        dataframe['ema_3'] = ta.EMA(dataframe, timeperiod=3)
        dataframe['ema_5'] = ta.EMA(dataframe, timeperiod=5)
        dataframe['ema_8'] = ta.EMA(dataframe, timeperiod=8)
        dataframe['ema_13'] = ta.EMA(dataframe, timeperiod=13)
        
        # Bollinger Bands for volatility scalping
        bb = ta.BBANDS(dataframe, timeperiod=10, nbdevup=1.5, nbdevdn=1.5)
        dataframe['bb_upper'] = bb['upperband']
        dataframe['bb_middle'] = bb['middleband']
        dataframe['bb_lower'] = bb['lowerband']
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']
        
        # Micro momentum indicators
        dataframe['mom_3'] = ta.MOM(dataframe, timeperiod=3)
        dataframe['mom_5'] = ta.MOM(dataframe, timeperiod=5)
        dataframe['roc_3'] = ta.ROC(dataframe, timeperiod=3)
        
        # Volume indicators for scalping confirmation
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=10).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        # Price action patterns
        dataframe['price_change'] = dataframe['close'].pct_change()
        dataframe['high_low_ratio'] = (dataframe['high'] - dataframe['low']) / dataframe['close']
        
        # Your original pivot logic (keeping it for compatibility)
        length = 25
        dataframe['pivot_high'] = dataframe['high'].rolling(window=length, min_periods=length).max().shift(1)
        dataframe['pivot_high'] = dataframe['pivot_high'].where(
            dataframe['high'].shift(1) == dataframe['pivot_high'], np.nan
        )
        
        dataframe['pivot_low'] = dataframe['low'].rolling(window=length, min_periods=length).min().shift(1)
        dataframe['pivot_low'] = dataframe['pivot_low'].where(
            dataframe['low'].shift(1) == dataframe['pivot_low'], np.nan
        )
        
        dataframe['high_swing'] = dataframe['pivot_high'].shift(1)
        dataframe['low_swing'] = dataframe['pivot_low'].shift(1)
        dataframe['high_swing'] = dataframe['high_swing'].fillna(0)
        dataframe['low_swing'] = dataframe['low_swing'].fillna(0)
        
        # Scalping-specific support/resistance (shorter periods)
        dataframe['micro_high'] = dataframe['high'].rolling(window=5).max()
        dataframe['micro_low'] = dataframe['low'].rolling(window=5).min()
        
        # Trend strength for quick entries
        dataframe['trend_up'] = (dataframe['ema_3'] > dataframe['ema_5']) & (dataframe['ema_5'] > dataframe['ema_8'])
        dataframe['trend_down'] = (dataframe['ema_3'] < dataframe['ema_5']) & (dataframe['ema_5'] < dataframe['ema_8'])
        
        # Volatility squeeze detection for breakout scalping
        dataframe['squeeze'] = dataframe['bb_width'] < dataframe['bb_width'].rolling(window=20).quantile(0.2)
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        
        # AGGRESSIVE LONG SCALPING ENTRIES - Multiple conditions for maximum opportunities
        
        # Condition 1: RSI Momentum Scalping
        long_rsi_momentum = (
            (qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6'])) |
            (qtpylib.crossed_above(dataframe['rsi_1'], 30)) |
            ((dataframe['rsi_3'] > dataframe['rsi_6']) & (dataframe['rsi_1'] > dataframe['rsi_3']))
        )
        
        # Condition 2: EMA Scalping (price above fast EMAs)
        long_ema_scalp = (
            (dataframe['close'] > dataframe['ema_3']) & (dataframe['ema_3'] > dataframe['ema_5']) |
            (qtpylib.crossed_above(dataframe['close'], dataframe['ema_3'])) |
            (qtpylib.crossed_above(dataframe['ema_3'], dataframe['ema_5']))
        )
        
        # Condition 3: Bollinger Band Scalping
        long_bb_scalp = (
            (qtpylib.crossed_above(dataframe['close'], dataframe['bb_lower'])) |
            ((dataframe['close'] < dataframe['bb_middle']) & (dataframe['close'] > dataframe['bb_lower']) & (dataframe['rsi_14'] < 35))
        )
        
        # Condition 4: Momentum Scalping
        long_momentum = (
            (dataframe['mom_3'] > 0) & (dataframe['mom_5'] > dataframe['mom_5'].shift(1)) |
            (dataframe['roc_3'] > 0.1) |
            (dataframe['price_change'] > 0.002)  # 0.2% price jump
        )
        
        # Condition 5: Volume Scalping
        long_volume = (
            (dataframe['volume_ratio'] > 1.2) |  # Volume spike
            (dataframe['volume'] > dataframe['volume'].shift(1) * 1.5)
        )
        
        # Condition 6: Squeeze Breakout
        long_squeeze = (
            (dataframe['squeeze'].shift(1) == True) & (dataframe['squeeze'] == False) & 
            (dataframe['close'] > dataframe['bb_middle'])
        )
        
        # Condition 7: Original Pivot Logic (for compatibility)
        long_pivot = (
            (qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['low_swing'] != 0) & 
            (dataframe['close'] > dataframe['low_swing'].shift(1))
        )
        
        # COMBINE ALL LONG CONDITIONS (OR logic for maximum entries)
        dataframe.loc[
            (long_rsi_momentum | long_ema_scalp | long_bb_scalp | long_momentum | long_volume | long_squeeze | long_pivot) &
            (dataframe['rsi_14'] < 85) &  # Not extremely overbought
            (dataframe['close'] > dataframe['micro_low'] * 1.001),  # Above recent micro support
            'enter_long'
        ] = 1

        # AGGRESSIVE SHORT SCALPING ENTRIES
        
        # Condition 1: RSI Momentum Scalping
        short_rsi_momentum = (
            (qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6'])) |
            (qtpylib.crossed_below(dataframe['rsi_1'], 70)) |
            ((dataframe['rsi_3'] < dataframe['rsi_6']) & (dataframe['rsi_1'] < dataframe['rsi_3']))
        )
        
        # Condition 2: EMA Scalping
        short_ema_scalp = (
            (dataframe['close'] < dataframe['ema_3']) & (dataframe['ema_3'] < dataframe['ema_5']) |
            (qtpylib.crossed_below(dataframe['close'], dataframe['ema_3'])) |
            (qtpylib.crossed_below(dataframe['ema_3'], dataframe['ema_5']))
        )
        
        # Condition 3: Bollinger Band Scalping
        short_bb_scalp = (
            (qtpylib.crossed_below(dataframe['close'], dataframe['bb_upper'])) |
            ((dataframe['close'] > dataframe['bb_middle']) & (dataframe['close'] < dataframe['bb_upper']) & (dataframe['rsi_14'] > 65))
        )
        
        # Condition 4: Momentum Scalping
        short_momentum = (
            (dataframe['mom_3'] < 0) & (dataframe['mom_5'] < dataframe['mom_5'].shift(1)) |
            (dataframe['roc_3'] < -0.1) |
            (dataframe['price_change'] < -0.002)  # 0.2% price drop
        )
        
        # Condition 5: Volume Scalping
        short_volume = (
            (dataframe['volume_ratio'] > 1.2) |  # Volume spike
            (dataframe['volume'] > dataframe['volume'].shift(1) * 1.5)
        )
        
        # Condition 6: Squeeze Breakout
        short_squeeze = (
            (dataframe['squeeze'].shift(1) == True) & (dataframe['squeeze'] == False) & 
            (dataframe['close'] < dataframe['bb_middle'])
        )
        
        # Condition 7: Original Pivot Logic
        short_pivot = (
            (qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['high_swing'] != 0) & 
            (dataframe['close'] < dataframe['high_swing'].shift(1))
        )
        
        # COMBINE ALL SHORT CONDITIONS
        dataframe.loc[
            (short_rsi_momentum | short_ema_scalp | short_bb_scalp | short_momentum | short_volume | short_squeeze | short_pivot) &
            (dataframe['rsi_14'] > 15) &  # Not extremely oversold
            (dataframe['close'] < dataframe['micro_high'] * 0.999),  # Below recent micro resistance
            'enter_short'
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        
        # AGGRESSIVE PROFIT-TAKING EXITS
        
        # Exit longs - Multiple quick profit conditions
        exit_long_conditions = (
            # RSI overbought
            (dataframe['rsi_3'] > 75) |
            (dataframe['rsi_1'] > 80) |
            
            # EMA resistance
            (dataframe['close'] > dataframe['ema_13'] * 1.005) |  # 0.5% above EMA13
            
            # Bollinger upper band
            (dataframe['close'] > dataframe['bb_upper'] * 0.998) |
            
            # Momentum exhaustion
            (dataframe['mom_3'] < dataframe['mom_3'].shift(1)) & (dataframe['mom_3'] > 0) |
            
            # Price target hit (micro resistance)
            (dataframe['close'] > dataframe['micro_high'] * 0.999) |
            
            # Quick reversal signals
            (qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6'])) |
            (qtpylib.crossed_below(dataframe['close'], dataframe['ema_3'])) |
            
            # Original pivot exit
            ((qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6'])) &
             (dataframe['high_swing'] != 0) & 
             (dataframe['close'] < dataframe['high_swing'].shift(1)))
        )
        
        # Exit shorts - Multiple quick profit conditions
        exit_short_conditions = (
            # RSI oversold
            (dataframe['rsi_3'] < 25) |
            (dataframe['rsi_1'] < 20) |
            
            # EMA support
            (dataframe['close'] < dataframe['ema_13'] * 0.995) |  # 0.5% below EMA13
            
            # Bollinger lower band
            (dataframe['close'] < dataframe['bb_lower'] * 1.002) |
            
            # Momentum exhaustion
            (dataframe['mom_3'] > dataframe['mom_3'].shift(1)) & (dataframe['mom_3'] < 0) |
            
            # Price target hit (micro support)
            (dataframe['close'] < dataframe['micro_low'] * 1.001) |
            
            # Quick reversal signals
            (qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6'])) |
            (qtpylib.crossed_above(dataframe['close'], dataframe['ema_3'])) |
            
            # Original pivot exit
            ((qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6'])) &
             (dataframe['low_swing'] != 0) & 
             (dataframe['close'] > dataframe['low_swing'].shift(1)))
        )
        
        dataframe.loc[exit_long_conditions, 'exit_long'] = 1
        dataframe.loc[exit_short_conditions, 'exit_short'] = 1
        
        return dataframe
    
    def custom_exit(self, pair: str, trade: Trade, current_time: datetime,
                   current_rate: float, current_profit: float, **kwargs) -> Optional[str]:
        """
        ULTRA-AGGRESSIVE SCALPING EXITS - Take every possible profit
        """
        
        # IMMEDIATE PROFIT TAKING - Even tiny profits
        if current_profit >= self.min_profit_scalp:  # 0.3% profit
            
            # Quick scalp (0.5% target)
            if current_profit >= self.quick_profit_target:
                return "quick_scalp_0.5%"
            
            # Get current market data for advanced exit logic
            try:
                dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
                if len(dataframe) < 2:
                    return None
                    
                current_candle = dataframe.iloc[-1]
                prev_candle = dataframe.iloc[-2]
                
                # Time-based quick exits (scalping shouldn't hold long)
                trade_duration_minutes = (current_time - trade.open_date).total_seconds() / 60
                
                # ULTRA-FAST EXITS (within first few minutes)
                if trade_duration_minutes <= 3:  # First 3 minutes
                    if current_profit >= 0.002:  # 0.2% profit in first 3 minutes
                        return "ultra_fast_scalp_3min"
                
                if trade_duration_minutes <= 5:  # First 5 minutes
                    if current_profit >= 0.003:  # 0.3% profit in first 5 minutes
                        return "fast_scalp_5min"
                
                if trade_duration_minutes <= 10:  # First 10 minutes
                    if current_profit >= 0.004:  # 0.4% profit in first 10 minutes
                        return "medium_scalp_10min"
                
                # RSI reversal exits
                if trade.is_short:
                    if (current_candle['rsi_1'] < 25 or current_candle['rsi_3'] < 30) and current_profit >= 0.003:
                        return "rsi_oversold_exit"
                    if current_candle['mom_3'] > 0 and current_profit >= 0.004:
                        return "momentum_reversal_exit"
                else:
                    if (current_candle['rsi_1'] > 75 or current_candle['rsi_3'] > 70) and current_profit >= 0.003:
                        return "rsi_overbought_exit"
                    if current_candle['mom_3'] < 0 and current_profit >= 0.004:
                        return "momentum_reversal_exit"
                
                # Bollinger Band exits
                if trade.is_short and current_rate <= current_candle['bb_lower'] * 1.001 and current_profit >= 0.003:
                    return "bb_lower_exit"
                if not trade.is_short and current_rate >= current_candle['bb_upper'] * 0.999 and current_profit >= 0.003:
                    return "bb_upper_exit"
                
                # Volume spike reversal
                if current_candle['volume_ratio'] > 2.0 and current_profit >= 0.004:
                    return "volume_spike_exit"
                
                # EMA resistance/support
                if trade.is_short and current_rate <= current_candle['ema_3'] * 0.998 and current_profit >= 0.003:
                    return "ema_support_exit"
                if not trade.is_short and current_rate >= current_candle['ema_3'] * 1.002 and current_profit >= 0.003:
                    return "ema_resistance_exit"
                
                # Price action reversal
                price_change = (current_rate - prev_candle['close']) / prev_candle['close']
                if trade.is_short and price_change < -0.003 and current_profit >= 0.003:  # 0.3% downward move
                    return "price_action_exit"
                if not trade.is_short and price_change > 0.003 and current_profit >= 0.003:  # 0.3% upward move
                    return "price_action_exit"
                
            except Exception:
                # If analysis fails, still take profit at targets
                pass
        
        # SCALED PROFIT TAKING
        if current_profit >= self.medium_profit_target:  # 1% profit
            return "medium_profit_1%"
        
        if current_profit >= self.major_profit_target:  # 2% profit
            return "major_profit_2%"
        
        # FORCED EXIT - Don't hold losing scalps too long
        trade_duration_minutes = (current_time - trade.open_date).total_seconds() / 60
        
        if trade_duration_minutes > 30:  # 30 minutes max hold time
            if current_profit >= 0.001:  # Any tiny profit after 30 minutes
                return "time_limit_profit_exit"
            elif current_profit > -0.01:  # Small loss after 30 minutes
                return "time_limit_small_loss_exit"
        
        if trade_duration_minutes > 60:  # 1 hour absolute max
            return "absolute_time_limit_exit"
        
        return None
    
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        AGGRESSIVE TRAILING STOPS for scalping
        """
        
        # Quick trailing once in profit
        if current_profit > 0.005:  # 0.5% profit
            # Tight trailing stop to lock in most profit
            return -0.003  # 0.3% trailing stop
        
        elif current_profit > 0.003:  # 0.3% profit
            # Very tight trailing
            return -0.002  # 0.2% trailing stop
        
        elif current_profit > 0.001:  # 0.1% profit
            # Breakeven+ trailing
            return -0.001  # 0.1% trailing stop
        
        # Keep your original stoploss for initial protection
        return self.stoploss
    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:
        """
        Keep your original leverage logic
        """
        return 20